# 数据可视化项目 - Pyecharts

这是一个基于Pyecharts的Python数据可视化项目，用于创建各种交互式图表。

## 环境设置

### Anaconda环境
- 环境名称: `chartest1`
- Python版本: 3.9

### 激活环境
```bash
conda activate chartest1
```

### 已安装的包
- pyecharts - 主要图表库
- pandas - 数据处理
- numpy - 数值计算
- matplotlib - 基础绘图
- seaborn - 统计可视化
- jupyter - 交互式开发

## 项目结构

```
data_visualization_project/
├── README.md                 # 项目说明
├── requirements.txt          # 依赖包列表
├── data/                     # 数据文件夹
│   └── sample_data.csv       # 示例数据
├── charts/                   # 图表脚本
│   ├── basic_charts.py       # 基础图表示例
│   ├── advanced_charts.py    # 高级图表示例
│   └── interactive_charts.py # 交互式图表示例
├── output/                   # 输出文件夹
│   └── html/                 # HTML图表输出
├── notebooks/                # Jupyter笔记本
│   └── data_analysis.ipynb   # 数据分析笔记本
└── utils/                    # 工具函数
    └── data_loader.py        # 数据加载工具
```

## 快速开始

1. 激活conda环境：
   ```bash
   conda activate chartest1
   ```

2. 运行基础图表示例：
   ```bash
   python charts/basic_charts.py
   ```

3. 启动Jupyter Notebook：
   ```bash
   jupyter notebook
   ```

## 支持的图表类型

- 柱状图 (Bar Chart)
- 折线图 (Line Chart)
- 饼图 (Pie Chart)
- 散点图 (Scatter Chart)
- 热力图 (Heatmap)
- 地图 (Map)
- 3D图表 (3D Charts)
- 仪表盘 (Dashboard)

## 使用说明

详细的使用说明和示例代码请参考各个脚本文件中的注释和文档。
