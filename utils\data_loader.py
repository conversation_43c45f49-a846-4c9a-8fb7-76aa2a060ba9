import pandas as pd
import os

def load_csv_data(file_path):
    """
    加载CSV数据文件
    
    参数:
        file_path (str): CSV文件路径
        
    返回:
        pandas.DataFrame: 加载的数据
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    try:
        data = pd.read_csv(file_path)
        print(f"成功加载数据: {file_path}")
        print(f"数据形状: {data.shape}")
        return data
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def get_sample_data():
    """
    获取示例数据
    
    返回:
        pandas.DataFrame: 示例数据
    """
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sample_data_path = os.path.join(current_dir, 'data', 'sample_data.csv')
    return load_csv_data(sample_data_path)

if __name__ == "__main__":
    # 测试数据加载
    data = get_sample_data()
    if data is not None:
        print(data.head())