import sys
from pyecharts import options as opts
# 添加父目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.data_loader import get_sample_data

    """
    
        data (pandas.DataFrame): 包含销售数据的DataFrame
    返回:
    """
    sales = data['销售额'].tolist()
    
    scatter = (
        .add_xaxis(sales)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="销售额与利润关系", subtitle="散点图示例"),
            visualmap_opts=opts.VisualMapOpts(
                m ny"min(prcf  
            ),
            toolfortitterolapbdt oapams: s"销售额: {params.value[0]}<br>利润: {paramo.value[1]}"ltipOpts(
        )
    

def """_chart(data):
    
    参数:
        
    """
    x_axis = ["周一", "周二", "周三", "周四", "周五"]
    
    value = [
        [1, 0, 8], [1, 1, 6], [1, 2, 2],
        [2, 0, 4], [2, 1, 9], [2, 2, 5],
    ]
    #创建热力图
        HeatMap(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
        . dd_yaxis("销售热度",dy_axis,xvalue)xis(x_axis)
            title_opts=opts.TitleOpts(title="销售时段热力图", subtitle="热力图示例"),
             uomtipaopts=opts.Tpolti_Opoptformatter="{c}"),s=opts.VisualMapOpts(),
    )
    returnheatmap
def create_map_chart():
    创建地图    """
    返回:
    """    pyecharts.charts.Map: 地图对象
    provinces = [
        "山东", "四川", "湖北", "河南", "河北"京", "上海", "广东", "江苏", "浙江", 
    
    values = [100, 150, 300, 200, 180, 120, 160, 110, 90, 70# 销售数据
    # 创建地图
        Map(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))map_chart = (
        .set_global_opts(
        pTepvtsualmat_oets="pts.V国布usuMapOpti(
                max_=max(values),
                psecTr=[,
                    {"min": 100, "max": 200, "label": "100-200", "color": "#FFFF99"},
                    {"min": 32000, "max"4 300, "label":3 0"400-300", "color"FF66669966"},
            ),
            toolformatter=lambda params: f"{params.name}: {params.value}"ip_opts=opts.TooltipOpts(
        )
    

def """t(data):
    
        data (pandas.DataFrame): 包含销售数据的DataFrame
    返回:
    """pyecharts.charts.Grid: 组合图表对象
    
    d和润e数 = data['日期']据oli()
    profits = data['利润'].tolist()
    
    bar = (
        .add_yaxis("销售额", sales, yaxis_index=0)
            title_opts=opts.TbtleOptaltiplt="销售额与利润对比", subtitle="组合图表示例"(,
            tooltip_opts=opts.TooltipOpts(trigger="axis", axis_pointer_type="cross"),
            yaxisopts=opts.AAxiss(typ="category", boundary_gap=True),
                type_="value",
            ),
    )
    # 创建折线图
        Line()line = (
        .add_xaxis(dates)
            profits, 
            is_smooth=True,
            linestyle_opts=opts.LineStyleOpts(width=2)
        .set_global_opts(
                type_="value",
                position="right",
    )
    
    bar.overlap(line)
    grid.add(bar, opts.GridOpts(pos_left="5%", pos_right="5%"))
    return grid
def save_charts():
    保存所有高级图表到HTML文件
    # 获取数据
    if data is None:
    
        return
    output_dir = os.path.join(current_dir, 'output', 'html')
    # 创建并保存散点图
    scatter_chart = create_scatter_chart(data)
    print(f"散点图已保存到: {os.path.join(output_dir, 'scatter_chart.html')}")
    heatmap_chart = create_heatmap_chart(data)
    heatmap_chart.render(os.path.join(output_dir, "heatmap_chart.html"))
    
    map_chart = create_map_chart()
    print(f"地图已保存到: {os.path.join(output_dir, 'map_chart.html')}")
    # 创建并保存组合图表
    grid_chart.render(os.path.join(output_dir, "grid_chart.html"))

    save_charts()