import os
import sys
from pyecharts.charts import Bar, Line, Pie
from pyecharts import options as opts
from pyecharts.globals import ThemeType

# 添加父目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入数据加载工具
from utils.data_loader import get_sample_data

def create_bar_chart(data):
    """
    创建基础柱状图
    
    参数:
        data (pandas.DataFrame): 包含销售数据的DataFrame
        
    返回:
        pyecharts.charts.Bar: 柱状图对象
    """
    # 提取日期和销售额数据
    dates = data['日期'].tolist()
    sales = data['销售额'].tolist()
    
    # 创建柱状图
    bar = (
        Bar(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
        .add_xaxis(dates)
        .add_yaxis("销售额", sales)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="每日销售额", subtitle="基础柱状图示例"),
            toolbox_opts=opts.ToolboxOpts(),
            datazoom_opts=opts.DataZoomOpts(),
        )
    )
    
    return bar

def create_line_chart(data):
    """
    创建基础折线图
    
    参数:
        data (pandas.DataFrame): 包含销售数据的DataFrame
        
    返回:
        pyecharts.charts.Line: 折线图对象
    """
    # 提取日期和利润数据
    dates = data['日期'].tolist()
    profits = data['利润'].tolist()
    
    # 创建折线图
    line = (
        Line(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
        .add_xaxis(dates)
        .add_yaxis("利润", profits, is_smooth=True)
        .set_global_opts(
            title_opts=opts.TitleOpts(title="每日利润", subtitle="基础折线图示例"),
            toolbox_opts=opts.ToolboxOpts(),
            datazoom_opts=opts.DataZoomOpts(),
        )
    )
    
    return line

def create_pie_chart(data):
    """
    创建基础饼图
    
    参数:
        data (pandas.DataFrame): 包含销售数据的DataFrame
        
    返回:
        pyecharts.charts.Pie: 饼图对象
    """
    # 按地区分组并计算销售额总和
    region_sales = data.groupby('地区')['销售额'].sum().reset_index()
    regions = region_sales['地区'].tolist()
    sales = region_sales['销售额'].tolist()
    
    # 创建饼图
    pie = (
        Pie(init_opts=opts.InitOpts(theme=ThemeType.LIGHT))
        .add(
            "",
            [list(z) for z in zip(regions, sales)],
            radius=["40%", "75%"],
        )
        .set_global_opts(
            title_opts=opts.TitleOpts(title="各地区销售额占比", subtitle="基础饼图示例"),
            legend_opts=opts.LegendOpts(orient="vertical", pos_top="15%", pos_left="2%"),
        )
        .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {c} ({d}%)"))
    )
    
    return pie

def save_charts():
    """
    保存所有图表到HTML文件
    """
    # 获取数据
    data = get_sample_data()
    if data is None:
        print("无法加载数据，退出程序")
        return
    
    # 创建输出目录
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    output_dir = os.path.join(current_dir, 'output', 'html')
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建并保存柱状图
    bar_chart = create_bar_chart(data)
    bar_chart.render(os.path.join(output_dir, "bar_chart.html"))
    print(f"柱状图已保存到: {os.path.join(output_dir, 'bar_chart.html')}")
    
    # 创建并保存折线图
    line_chart = create_line_chart(data)
    line_chart.render(os.path.join(output_dir, "line_chart.html"))
    print(f"折线图已保存到: {os.path.join(output_dir, 'line_chart.html')}")
    
    # 创建并保存饼图
    pie_chart = create_pie_chart(data)
    pie_chart.render(os.path.join(output_dir, "pie_chart.html"))
    print(f"饼图已保存到: {os.path.join(output_dir, 'pie_chart.html')}")

if __name__ == "__main__":
    save_charts()